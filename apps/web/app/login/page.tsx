// app/login/page.tsx

import SetPassword from "@workspace/ui/components/SetPassword";
import ThemeToggleButton from "@workspace/ui/components/theme-toggle-button";
import Image from "next/image";

export default function LoginPage() {
  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-background p-4">
      <div className="absolute top-4 right-4">
        <ThemeToggleButton start="top-right" />
      </div>

      <div className="w-full max-w-7xl bg-card rounded-2xl shadow-xl border overflow-hidden grid grid-cols-1 md:grid-cols-[2fr_3fr] min-h-[50rem]">
        {/* Left - Image */}
        <div className="relative">
          <Image
            fill
            src="/images/signin_img.jpg"
            alt="Doctor smiling"
            className="object-cover"
            priority
          />
        </div>

        {/* Right - Form */}
        <div className="flex justify-center items-center">
          <div className="space-y-6 w-md">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-foreground">Sign In</h2>
              <p className="mt-2 text-muted-foreground">
                Welcome back! Please sign in to your account.
              </p>
            </div>

            <form className="space-y-5">
              <div>
                <label className="block text-sm font-medium text-foreground mb-1">
                  Email
                </label>
                <input
                  type="email"
                  className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Enter your email"
                />
              </div>

              <div>
                <SetPassword />
              </div>

              <button
                type="submit"
                className="w-full bg-primary text-primary-foreground py-2 px-4 rounded-md hover:bg-primary/90 transition-colors font-medium"
              >
                Sign In
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
